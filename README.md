# Clinical Evaluation Assistant

A Clinical Evaluation assistant for medical devices (EU MDR/IVDR compliance) built with Next.js, TypeScript, and Tailwind CSS.

## Features

This application helps with clinical evaluation of medical devices by:

1. **Validating Intended Use Statements** - Confirms whether the provided text is a valid "Intended Use / Intended Purpose" statement
2. **Structured Information Extraction** - Extracts and organizes information into JSON format with:
   - Device synonyms, brand names, generic terms, technical variations
   - Condition synonyms/related terms for the target disease/indication
   - Relevant target populations (adults, pediatrics, geriatrics)
   - Relevant outcomes for safety and performance
3. **Search Query Generation** - Creates database-ready Boolean search queries for:
   - **PubMed**: Uses MeSH terms where possible, plus Boolean operators
   - **Cochrane Library**: Uses Boolean operators and phrase searching
   - **Google Scholar**: Uses quotes for exact phrases, simple OR logic

## Getting Started

First, run the development server:

```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the application.

## Example Usage

**Input:**

- Product Name: `Insulin Pump`
- Intended Use: `The device is intended for continuous subcutaneous infusion of insulin for the treatment of Type 1 diabetes mellitus in adults and pediatric patients.`

**Output:**

- Intended Use check: ✅ YES - Valid intended use statement
- Structured JSON data with extracted Device, Condition, Population, and Outcomes
- Database-specific search queries for PubMed, Cochrane, and Google Scholar

## Technology Stack

- **Next.js 15** - React framework with App Router
- **TypeScript** - Type safety and better development experience
- **Tailwind CSS** - Utility-first CSS framework
- **React Hooks** - State management and side effects

## Project Structure

```
src/
├── app/                    # Next.js App Router pages
├── components/             # React components
│   └── ClinicalEvaluationForm.tsx
├── lib/                    # Business logic and utilities
│   └── clinical-evaluation.ts
└── types/                  # TypeScript type definitions
    └── clinical-evaluation.ts
```

## Features

- ✅ Real-time validation of intended use statements
- ✅ Intelligent extraction of medical device information
- ✅ Generation of database-specific search queries
- ✅ Copy-to-clipboard functionality for search queries
- ✅ Responsive design for desktop and mobile
- ✅ Clean, professional UI suitable for regulatory work
