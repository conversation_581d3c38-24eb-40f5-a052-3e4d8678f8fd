# Clinical Evaluation Assistant

A Clinical Evaluation assistant for medical devices (EU MDR/IVDR compliance) built with Next.js, TypeScript, and Tailwind CSS.

## Features

This application helps with clinical evaluation of medical devices by:

1. **Validating Intended Use Statements** - Confirms whether the provided text is a valid "Intended Use / Intended Purpose" statement
2. **Structured Information Extraction** - Extracts and organizes information into JSON format with:
   - Device synonyms, brand names, generic terms, technical variations
   - Condition synonyms/related terms for the target disease/indication
   - Relevant target populations (adults, pediatrics, geriatrics)
   - Relevant outcomes for safety and performance
3. **Search Query Generation** - Creates database-ready Boolean search queries for:
   - **PubMed**: Uses MeSH terms where possible, plus Boolean operators
   - **Cochrane Library**: Uses Boolean operators and phrase searching
   - **Google Scholar**: Uses quotes for exact phrases, simple OR logic

## Setup

1. **Install dependencies:**

```bash
npm install
```

2. **Configure OpenAI API:**

   - Copy `.env.example` to `.env.local`
   - Add your OpenAI API key:

   ```
   OPENAI_API_KEY=sk-your-openai-api-key-here
   ```

3. **Run the development server:**

```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the application.

## Example Usage

**Input:**

- Product Name: `Insulin Pump`
- Intended Use: `The device is intended for continuous subcutaneous infusion of insulin for the treatment of Type 1 diabetes mellitus in adults and pediatric patients.`

**Output:**

- Intended Use check: ✅ YES - Valid intended use statement
- Structured JSON data with extracted Device, Condition, Population, and Outcomes
- Database-specific search queries for PubMed, Cochrane, and Google Scholar

## Technology Stack

- **Next.js 15** - React framework with App Router
- **TypeScript** - Type safety and better development experience
- **Tailwind CSS** - Utility-first CSS framework
- **OpenAI API** - AI-powered information extraction and query generation
- **React Hooks** - State management and side effects

## Project Structure

```
src/
├── app/                    # Next.js App Router pages
│   ├── api/               # API routes
│   │   └── clinical-evaluation/
│   │       └── route.ts   # OpenAI API integration
│   ├── layout.tsx
│   └── page.tsx
├── components/             # React components
│   └── ClinicalEvaluationForm.tsx
├── lib/                    # Business logic and utilities
│   ├── clinical-evaluation.ts  # Rule-based fallback logic
│   └── openai.ts              # OpenAI API integration
├── types/                  # TypeScript type definitions
│   └── clinical-evaluation.ts
└── tests/                  # Test files
    └── clinical-evaluation.test.ts
```

## How It Works

### AI-Powered Mode (Recommended)

When `OPENAI_API_KEY` is configured:

1. Uses OpenAI GPT-4o-mini for intelligent analysis
2. Comprehensive system prompt ensures medical accuracy
3. Extracts more nuanced synonyms and medical terminology
4. Generates optimized database-specific queries

### Fallback Mode

When OpenAI API is unavailable:

1. Uses rule-based pattern matching
2. Predefined medical device and condition mappings
3. Still generates functional search queries
4. Ensures the application always works

The application automatically detects which mode to use and displays the method used in the results.

## Features

- ✅ **AI-Powered Analysis** - Uses OpenAI GPT-4o-mini for intelligent processing
- ✅ **Real-time validation** of intended use statements
- ✅ **Intelligent extraction** of medical device information with comprehensive synonyms
- ✅ **Database-specific search queries** optimized for PubMed, Cochrane, and Google Scholar
- ✅ **Copy-to-clipboard functionality** for search queries
- ✅ **Responsive design** for desktop and mobile
- ✅ **Professional UI** suitable for regulatory work
- ✅ **Comprehensive system prompt** for consistent, accurate medical terminology extraction
