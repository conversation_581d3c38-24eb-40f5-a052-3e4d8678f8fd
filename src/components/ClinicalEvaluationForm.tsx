"use client";

import { useState } from "react";
import {
  ClinicalEvaluationResult,
  SearchKeyword,
} from "../types/clinical-evaluation";

export default function ClinicalEvaluationForm() {
  const [productName, setProductName] = useState("");
  const [intendedUse, setIntendedUse] = useState("");
  const [result, setResult] = useState<ClinicalEvaluationResult | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      const response = await fetch("/api/clinical-evaluation", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          productName,
          intendedUse,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(
          errorData.error || "Failed to perform clinical evaluation"
        );
      }

      const evaluationResult = await response.json();
      setResult(evaluationResult);
    } catch (error) {
      console.error("Error performing clinical evaluation:", error);
      alert(error instanceof Error ? error.message : "An error occurred");
    } finally {
      setIsLoading(false);
    }
  };

  const handleClear = () => {
    setProductName("");
    setIntendedUse("");
    setResult(null);
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          Clinical Evaluation Assistant
        </h1>
        <p className="text-gray-600">
          EU MDR/IVDR Compliance Tool for Medical Device Clinical Evaluation
        </p>
        <p className="text-sm text-blue-600 font-medium">
          ✨ Powered by AI for intelligent information extraction and search
          query generation
        </p>
      </div>

      <form
        onSubmit={handleSubmit}
        className="space-y-6 bg-white p-6 rounded-lg shadow-md"
      >
        <div>
          <label
            htmlFor="productName"
            className="block text-sm font-medium text-gray-700 mb-2"
          >
            Product Name
          </label>
          <input
            type="text"
            id="productName"
            value={productName}
            onChange={(e) => setProductName(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 text-black focus:ring-blue-500 focus:border-blue-500"
            placeholder="Enter the medical device product name"
            required
          />
        </div>

        <div>
          <label
            htmlFor="intendedUse"
            className="block text-sm font-medium text-gray-700 mb-2"
          >
            Intended Use Statement
          </label>
          <textarea
            id="intendedUse"
            value={intendedUse}
            onChange={(e) => setIntendedUse(e.target.value)}
            rows={4}
            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-black"
            placeholder="Copy and paste the intended use statement from the device manual"
            required
          />
        </div>

        <div className="flex gap-4">
          <button
            type="submit"
            disabled={isLoading}
            className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isLoading ? "Analyzing with AI..." : "Analyze with AI"}
          </button>
          <button
            type="button"
            onClick={handleClear}
            className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          >
            Clear
          </button>
        </div>
      </form>

      {result && (
        <div className="space-y-6">
          {/* Validation Result */}
          <div className="bg-white p-6 rounded-lg shadow-md">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-semibold text-gray-900">
                Step 1: Intended Use Validation
              </h2>
              {result.aiPowered !== undefined && (
                <span
                  className={`text-xs px-2 py-1 rounded-full ${
                    result.aiPowered
                      ? "bg-blue-100 text-blue-800"
                      : "bg-gray-100 text-gray-800"
                  }`}
                >
                  {result.aiPowered ? "🤖 AI-Powered" : "📋 Rule-Based"}
                </span>
              )}
            </div>
            <div className="flex items-center space-x-2">
              <span
                className={`text-lg font-bold ${
                  result.validation.isValid ? "text-green-600" : "text-red-600"
                }`}
              >
                {result.validation.isValid ? "YES ✅" : "NO ❌"}
              </span>
              <span className="text-gray-600">
                - {result.validation.comment}
              </span>
            </div>
          </div>

          {result.validation.isValid && (
            <>
              {/* Extracted Data */}
              <div className="bg-white p-6 rounded-lg shadow-md">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">
                  Step 2: Structured Information Extraction
                </h2>
                <div className="bg-gray-50 p-4 rounded-md">
                  <pre className="text-sm text-gray-800 whitespace-pre-wrap">
                    {JSON.stringify(result.extractedData, null, 2)}
                  </pre>
                </div>
              </div>

              {/* Search Keywords Table */}
              <div className="bg-white p-6 rounded-lg shadow-md">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">
                  Step 3: Search Keywords for Literature Review
                </h2>

                <div className="overflow-x-auto">
                  <table className="min-w-full border border-gray-300">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="border border-gray-300 px-4 py-2 text-left text-sm font-medium text-gray-900">
                          Code
                        </th>
                        <th className="border border-gray-300 px-4 py-2 text-left text-sm font-medium text-gray-900">
                          Keywords
                        </th>
                        <th className="border border-gray-300 px-4 py-2 text-left text-sm font-medium text-gray-900">
                          Database
                        </th>
                        <th className="border border-gray-300 px-4 py-2 text-left text-sm font-medium text-gray-900">
                          Date of Search
                        </th>
                        <th className="border border-gray-300 px-4 py-2 text-left text-sm font-medium text-gray-900">
                          PubMed
                        </th>
                        <th className="border border-gray-300 px-4 py-2 text-left text-sm font-medium text-gray-900">
                          Cochrane
                        </th>
                        <th className="border border-gray-300 px-4 py-2 text-left text-sm font-medium text-gray-900">
                          Duplicates
                        </th>
                        <th className="border border-gray-300 px-4 py-2 text-left text-sm font-medium text-gray-900">
                          Final Articles
                        </th>
                        <th className="border border-gray-300 px-4 py-2 text-left text-sm font-medium text-gray-900">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      {result.searchQueries.keywords.map((keyword) => (
                        <KeywordRow key={keyword.id} keyword={keyword} />
                      ))}
                    </tbody>
                  </table>
                </div>

                {result.searchQueries.keywords.length === 0 && (
                  <div className="text-center py-8 text-gray-500">
                    No search keywords generated
                  </div>
                )}
              </div>
            </>
          )}
        </div>
      )}
    </div>
  );
}

interface KeywordRowProps {
  keyword: SearchKeyword;
}

function KeywordRow({ keyword }: KeywordRowProps) {
  const [copied, setCopied] = useState(false);

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(keyword.keyword);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      console.error("Failed to copy keyword:", error);
    }
  };

  return (
    <tr className="hover:bg-gray-50">
      <td className="border border-gray-300 px-4 py-2 text-sm font-medium text-gray-900">
        {keyword.id}
      </td>
      <td className="border border-gray-300 px-4 py-2 text-sm text-gray-900 max-w-md">
        <div className="break-words font-mono text-xs">{keyword.keyword}</div>
      </td>
      <td className="border border-gray-300 px-4 py-2 text-sm text-gray-900">
        {keyword.database}
      </td>
      <td className="border border-gray-300 px-4 py-2 text-sm text-gray-900">
        {keyword.searchDate}
      </td>
      <td className="border border-gray-300 px-4 py-2 text-sm text-gray-900 text-center">
        {keyword.pubmedResults ?? "-"}
      </td>
      <td className="border border-gray-300 px-4 py-2 text-sm text-gray-900 text-center">
        {keyword.cochraneResults ?? "-"}
      </td>
      <td className="border border-gray-300 px-4 py-2 text-sm text-gray-900 text-center">
        {keyword.duplicates ?? "-"}
      </td>
      <td className="border border-gray-300 px-4 py-2 text-sm text-gray-900 text-center">
        {keyword.finalArticles ?? "-"}
      </td>
      <td className="border border-gray-300 px-4 py-2 text-sm text-gray-900">
        <button
          onClick={handleCopy}
          className="px-2 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700 focus:outline-none focus:ring-1 focus:ring-blue-500"
        >
          {copied ? "Copied!" : "Copy"}
        </button>
      </td>
    </tr>
  );
}
