"use client";

import { useState } from "react";
import { performClinicalEvaluation } from "../lib/clinical-evaluation";
import { ClinicalEvaluationResult } from "../types/clinical-evaluation";

export default function ClinicalEvaluationForm() {
  const [productName, setProductName] = useState("");
  const [intendedUse, setIntendedUse] = useState("");
  const [result, setResult] = useState<ClinicalEvaluationResult | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      const evaluationResult = performClinicalEvaluation({
        productName,
        intendedUse,
      });
      setResult(evaluationResult);
    } catch (error) {
      console.error("Error performing clinical evaluation:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleClear = () => {
    setProductName("");
    setIntendedUse("");
    setResult(null);
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          Clinical Evaluation Assistant
        </h1>
        <p className="text-gray-600">
          EU MDR/IVDR Compliance Tool for Medical Device Clinical Evaluation
        </p>
      </div>

      <form
        onSubmit={handleSubmit}
        className="space-y-6 bg-white p-6 rounded-lg shadow-md"
      >
        <div>
          <label
            htmlFor="productName"
            className="block text-sm font-medium text-gray-700 mb-2"
          >
            Product Name
          </label>
          <input
            type="text"
            id="productName"
            value={productName}
            onChange={(e) => setProductName(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            placeholder="Enter the medical device product name"
            required
          />
        </div>

        <div>
          <label
            htmlFor="intendedUse"
            className="block text-sm font-medium text-gray-700 mb-2"
          >
            Intended Use Statement
          </label>
          <textarea
            id="intendedUse"
            value={intendedUse}
            onChange={(e) => setIntendedUse(e.target.value)}
            rows={4}
            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            placeholder="Copy and paste the intended use statement from the device manual"
            required
          />
        </div>

        <div className="flex gap-4">
          <button
            type="submit"
            disabled={isLoading}
            className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isLoading ? "Analyzing..." : "Analyze"}
          </button>
          <button
            type="button"
            onClick={handleClear}
            className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          >
            Clear
          </button>
        </div>
      </form>

      {result && (
        <div className="space-y-6">
          {/* Validation Result */}
          <div className="bg-white p-6 rounded-lg shadow-md">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">
              Step 1: Intended Use Validation
            </h2>
            <div className="flex items-center space-x-2">
              <span
                className={`text-lg font-bold ${
                  result.validation.isValid ? "text-green-600" : "text-red-600"
                }`}
              >
                {result.validation.isValid ? "YES ✅" : "NO ❌"}
              </span>
              <span className="text-gray-600">
                - {result.validation.comment}
              </span>
            </div>
          </div>

          {result.validation.isValid && (
            <>
              {/* Extracted Data */}
              <div className="bg-white p-6 rounded-lg shadow-md">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">
                  Step 2: Structured Information Extraction
                </h2>
                <div className="bg-gray-50 p-4 rounded-md">
                  <pre className="text-sm text-gray-800 whitespace-pre-wrap">
                    {JSON.stringify(result.extractedData, null, 2)}
                  </pre>
                </div>
              </div>

              {/* Search Queries */}
              <div className="bg-white p-6 rounded-lg shadow-md">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">
                  Step 3: Database Search Queries
                </h2>

                <div className="space-y-6">
                  <QuerySection
                    title="PubMed Query"
                    query={result.searchQueries.pubmed}
                  />
                  <QuerySection
                    title="Cochrane Query"
                    query={result.searchQueries.cochrane}
                  />
                  <QuerySection
                    title="Google Scholar Query"
                    query={result.searchQueries.googleScholar}
                  />
                </div>
              </div>
            </>
          )}
        </div>
      )}
    </div>
  );
}

interface QuerySectionProps {
  title: string;
  query: string;
}

function QuerySection({ title, query }: QuerySectionProps) {
  const [copied, setCopied] = useState(false);

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(query);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      console.error("Failed to copy text:", error);
    }
  };

  return (
    <div>
      <div className="flex items-center justify-between mb-2">
        <h3 className="text-lg font-medium text-gray-900">{title}</h3>
        <button
          onClick={handleCopy}
          className="px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
        >
          {copied ? "Copied!" : "Copy"}
        </button>
      </div>
      <div className="bg-gray-50 p-4 rounded-md border">
        <pre className="text-sm text-gray-800 whitespace-pre-wrap font-mono">
          {query}
        </pre>
      </div>
    </div>
  );
}
