import { NextRequest, NextResponse } from "next/server";
import { performAIClinicalEvaluation } from "../../../lib/openai";
import { performClinicalEvaluation } from "../../../lib/clinical-evaluation";
import { ClinicalEvaluationInput } from "../../../types/clinical-evaluation";

export async function POST(request: NextRequest) {
  try {
    const body: ClinicalEvaluationInput = await request.json();

    // Validate input
    if (!body.productName || !body.intendedUse) {
      return NextResponse.json(
        { error: "Product name and intended use are required" },
        { status: 400 }
      );
    }

    let result;

    // Try OpenAI API first if configured
    if (process.env.OPENAI_API_KEY) {
      try {
        result = await performAIClinicalEvaluation(
          body.productName,
          body.intendedUse
        );
        // Add a flag to indicate AI was used
        result.aiPowered = true;
      } catch (aiError) {
        console.warn(
          "OpenAI API failed, falling back to rule-based approach:",
          aiError
        );
        // Fall back to rule-based approach
        result = performClinicalEvaluation(body);
        result.aiPowered = false;
      }
    } else {
      // Use rule-based approach if no API key
      result = performClinicalEvaluation(body);
      result.aiPowered = false;
    }

    return NextResponse.json(result);
  } catch (error) {
    console.error("API Error:", error);
    return NextResponse.json(
      { error: "Failed to perform clinical evaluation" },
      { status: 500 }
    );
  }
}
