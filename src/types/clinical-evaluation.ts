export interface ClinicalEvaluationData {
  Device: string[];
  Condition: string[];
  Population: string[];
  Outcomes: string[];
}

export interface ValidationResult {
  isValid: boolean;
  comment: string;
}

export interface SearchKeyword {
  id: string;
  keyword: string;
  database: "PubMed" | "Cochrane" | "Google Scholar";
  searchDate: string;
  pubmedResults?: number;
  cochraneResults?: number;
  duplicates?: number;
  finalArticles?: number;
}

export interface SearchQueries {
  keywords: SearchKeyword[];
}

export interface ClinicalEvaluationResult {
  validation: ValidationResult;
  extractedData: ClinicalEvaluationData;
  searchQueries: SearchQueries;
  aiPowered?: boolean;
}

export interface ClinicalEvaluationInput {
  productName: string;
  intendedUse: string;
}
