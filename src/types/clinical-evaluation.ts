export interface ClinicalEvaluationData {
  Device: string[];
  Condition: string[];
  Population: string[];
  Outcomes: string[];
}

export interface ValidationResult {
  isValid: boolean;
  comment: string;
}

export interface SearchQueries {
  pubmed: string;
  cochrane: string;
  googleScholar: string;
}

export interface ClinicalEvaluationResult {
  validation: ValidationResult;
  extractedData: ClinicalEvaluationData;
  searchQueries: SearchQueries;
  aiPowered?: boolean;
}

export interface ClinicalEvaluationInput {
  productName: string;
  intendedUse: string;
}
