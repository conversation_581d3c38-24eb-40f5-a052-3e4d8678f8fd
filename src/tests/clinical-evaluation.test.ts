import { performClinicalEvaluation } from "../lib/clinical-evaluation";

// Example test case - Insulin Pump
const insulinPumpExample = {
  productName: "Insulin Pump",
  intendedUse:
    "The device is intended for continuous subcutaneous infusion of insulin for the treatment of Type 1 diabetes mellitus in adults and pediatric patients.",
};

console.log("=== CLINICAL EVALUATION ASSISTANT TEST (FALLBACK MODE) ===\n");

// Test the fallback (rule-based) method
console.log("Testing Rule-Based Approach (Fallback):");
const result = performClinicalEvaluation(insulinPumpExample);

console.log("Input:");
console.log(`Product Name: ${insulinPumpExample.productName}`);
console.log(`Intended Use: ${insulinPumpExample.intendedUse}\n`);

console.log("=== RESULTS ===\n");

console.log("1. Intended Use Validation:");
console.log(
  `${result.validation.isValid ? "YES ✅" : "NO ❌"} - ${
    result.validation.comment
  }\n`
);

if (result.validation.isValid) {
  console.log("2. Structured Information Extraction:");
  console.log(JSON.stringify(result.extractedData, null, 2));
  console.log("\n3. Search Keywords:\n");

  result.searchQueries.keywords.forEach((keyword, index) => {
    console.log(`${keyword.id}: ${keyword.keyword}`);
    console.log(`Database: ${keyword.database}`);
    console.log(`Date: ${keyword.searchDate}\n`);
  });
}

console.log("\n=== FALLBACK TEST COMPLETED ===");
console.log(
  "\n📝 Note: To test AI mode, configure OPENAI_API_KEY in .env.local and use the web interface at http://localhost:3000"
);
