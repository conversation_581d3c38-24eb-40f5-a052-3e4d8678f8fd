import { performClinicalEvaluation } from "../lib/clinical-evaluation";

// Example test case - Insulin Pump
const insulinPumpExample = {
  productName: "Insulin Pump",
  intendedUse:
    "The device is intended for continuous subcutaneous infusion of insulin for the treatment of Type 1 diabetes mellitus in adults and pediatric patients.",
};

// Run the clinical evaluation
const result = performClinicalEvaluation(insulinPumpExample);

console.log("=== CLINICAL EVALUATION ASSISTANT TEST ===\n");

console.log("Input:");
console.log(`Product Name: ${insulinPumpExample.productName}`);
console.log(`Intended Use: ${insulinPumpExample.intendedUse}\n`);

console.log("=== RESULTS ===\n");

console.log("1. Intended Use Validation:");
console.log(
  `${result.validation.isValid ? "YES ✅" : "NO ❌"} - ${
    result.validation.comment
  }\n`
);

if (result.validation.isValid) {
  console.log("2. Structured Information Extraction:");
  console.log(JSON.stringify(result.extractedData, null, 2));
  console.log("\n3. Database Search Queries:\n");

  console.log("PubMed Query:");
  console.log(result.searchQueries.pubmed);
  console.log("\nCochrane Query:");
  console.log(result.searchQueries.cochrane);
  console.log("\nGoogle Scholar Query:");
  console.log(result.searchQueries.googleScholar);
}

console.log("\n=== TEST COMPLETED ===");
