import {
  ClinicalEvaluationInput,
  ClinicalEvaluationResult,
  ClinicalEvaluationData,
  ValidationResult,
  SearchQueries,
} from "../types/clinical-evaluation";

/**
 * Validates if the provided text is a valid "Intended Use / Intended Purpose" statement
 */
export function validateIntendedUse(intendedUse: string): ValidationResult {
  const text = intendedUse.trim().toLowerCase();

  // Check if the text is empty
  if (!text) {
    return {
      isValid: false,
      comment: "Intended use statement cannot be empty",
    };
  }

  // Check for key indicators of an intended use statement
  const intendedUseIndicators = [
    "intended for",
    "intended to",
    "designed for",
    "used for",
    "indicated for",
    "purpose",
    "treatment",
    "diagnosis",
    "monitoring",
    "therapy",
  ];

  const hasIndicators = intendedUseIndicators.some((indicator) =>
    text.includes(indicator)
  );

  if (!hasIndicators) {
    return {
      isValid: false,
      comment:
        "Text does not appear to be a valid intended use statement. Should include purpose, indication, or intended application.",
    };
  }

  // Check minimum length (should be descriptive)
  if (text.length < 20) {
    return {
      isValid: false,
      comment:
        "Intended use statement is too brief. Should provide clear description of device purpose and application.",
    };
  }

  return {
    isValid: true,
    comment: "Valid intended use statement",
  };
}

/**
 * Extracts structured information from product name and intended use statement
 */
export function extractClinicalData(
  productName: string,
  intendedUse: string
): ClinicalEvaluationData {
  const text = `${productName} ${intendedUse}`.toLowerCase();

  // Extract device-related terms
  const deviceTerms = extractDeviceTerms(productName, text);

  // Extract condition-related terms
  const conditionTerms = extractConditionTerms(text);

  // Extract population-related terms
  const populationTerms = extractPopulationTerms(text);

  // Extract outcome-related terms
  const outcomeTerms = extractOutcomeTerms(text);

  return {
    Device: deviceTerms,
    Condition: conditionTerms,
    Population: populationTerms,
    Outcomes: outcomeTerms,
  };
}

/**
 * Extract device-related terms and synonyms
 */
function extractDeviceTerms(productName: string, text: string): string[] {
  const terms = new Set<string>();

  // Add the product name itself
  terms.add(productName);

  // Common device type mappings
  const deviceMappings: Record<string, string[]> = {
    pump: ["Infusion Pump", "External Pump", "Portable Pump"],
    monitor: ["Monitoring Device", "Monitor", "Monitoring System"],
    catheter: ["Catheter", "Catheter System", "Intravascular Catheter"],
    stent: ["Stent", "Vascular Stent", "Coronary Stent"],
    implant: ["Implant", "Medical Implant", "Surgical Implant"],
    pacemaker: ["Pacemaker", "Cardiac Pacemaker", "Implantable Pacemaker"],
    defibrillator: [
      "Defibrillator",
      "ICD",
      "Implantable Cardioverter Defibrillator",
    ],
    ventilator: ["Ventilator", "Mechanical Ventilator", "Respiratory Support"],
    dialysis: ["Dialysis Machine", "Hemodialysis", "Peritoneal Dialysis"],
    scanner: ["Scanner", "Imaging System", "Diagnostic Scanner"],
    ultrasound: ["Ultrasound", "Ultrasound System", "Sonography"],
    "x-ray": ["X-ray", "Radiography", "X-ray System"],
    mri: ["MRI", "Magnetic Resonance Imaging", "MR Scanner"],
    ct: ["CT", "Computed Tomography", "CT Scanner"],
    insulin: [
      "Insulin Pump",
      "Continuous Subcutaneous Insulin Infusion",
      "CSII",
    ],
  };

  // Check for device type matches
  Object.entries(deviceMappings).forEach(([key, synonyms]) => {
    if (text.includes(key)) {
      synonyms.forEach((synonym) => terms.add(synonym));
    }
  });

  return Array.from(terms);
}

/**
 * Extract condition-related terms and synonyms
 */
function extractConditionTerms(text: string): string[] {
  const terms = new Set<string>();

  const conditionMappings: Record<string, string[]> = {
    diabetes: [
      "Diabetes Mellitus",
      "Type 1 Diabetes",
      "Type 2 Diabetes",
      "T1DM",
      "T2DM",
      "IDDM",
      "NIDDM",
    ],
    hypertension: [
      "Hypertension",
      "High Blood Pressure",
      "HTN",
      "Arterial Hypertension",
    ],
    "heart failure": [
      "Heart Failure",
      "Cardiac Failure",
      "CHF",
      "Congestive Heart Failure",
    ],
    arrhythmia: ["Arrhythmia", "Cardiac Arrhythmia", "Heart Rhythm Disorder"],
    cancer: ["Cancer", "Malignancy", "Tumor", "Neoplasm", "Carcinoma"],
    infection: [
      "Infection",
      "Bacterial Infection",
      "Sepsis",
      "Infectious Disease",
    ],
    kidney: [
      "Kidney Disease",
      "Renal Disease",
      "Chronic Kidney Disease",
      "CKD",
      "ESRD",
    ],
    respiratory: ["Respiratory Disease", "COPD", "Asthma", "Pulmonary Disease"],
    cardiovascular: [
      "Cardiovascular Disease",
      "CVD",
      "Coronary Artery Disease",
      "CAD",
    ],
  };

  Object.entries(conditionMappings).forEach(([key, synonyms]) => {
    if (text.includes(key)) {
      synonyms.forEach((synonym) => terms.add(synonym));
    }
  });

  return Array.from(terms);
}

/**
 * Extract population-related terms
 */
function extractPopulationTerms(text: string): string[] {
  const terms = new Set<string>();

  const populationTerms = [
    "adults",
    "adult",
    "children",
    "pediatric",
    "pediatrics",
    "geriatric",
    "elderly",
    "neonatal",
    "infant",
    "adolescent",
    "pregnant",
    "pregnancy",
  ];

  populationTerms.forEach((term) => {
    if (text.includes(term)) {
      if (term.includes("pediatric") || term.includes("children")) {
        terms.add("Pediatric Patients");
        terms.add("Children");
      } else if (term.includes("adult")) {
        terms.add("Adults");
      } else if (term.includes("geriatric") || term.includes("elderly")) {
        terms.add("Geriatric Patients");
        terms.add("Elderly");
      } else if (term.includes("neonatal") || term.includes("infant")) {
        terms.add("Neonatal Patients");
        terms.add("Infants");
      } else if (term.includes("pregnant") || term.includes("pregnancy")) {
        terms.add("Pregnant Women");
      }
    }
  });

  // Default to adults if no specific population mentioned
  if (terms.size === 0) {
    terms.add("Adults");
  }

  return Array.from(terms);
}

/**
 * Extract outcome-related terms
 */
function extractOutcomeTerms(text: string): string[] {
  const terms = new Set<string>();

  // Always include basic safety and efficacy outcomes
  terms.add("Safety");
  terms.add("Efficacy");
  terms.add("Adverse Events");
  terms.add("Complications");

  // Condition-specific outcomes
  if (text.includes("diabetes")) {
    terms.add("Glycemic Control");
    terms.add("HbA1c");
    terms.add("Hypoglycemia");
    terms.add("Blood Glucose");
  }

  if (text.includes("heart") || text.includes("cardiac")) {
    terms.add("Cardiac Function");
    terms.add("Ejection Fraction");
    terms.add("Mortality");
    terms.add("Hospitalization");
  }

  if (text.includes("blood pressure") || text.includes("hypertension")) {
    terms.add("Blood Pressure Control");
    terms.add("Systolic Blood Pressure");
    terms.add("Diastolic Blood Pressure");
  }

  if (text.includes("infection")) {
    terms.add("Infection Rate");
    terms.add("Antimicrobial Resistance");
    terms.add("Clinical Cure");
  }

  return Array.from(terms);
}

/**
 * Generate database-specific search queries
 */
export function generateSearchQueries(
  data: ClinicalEvaluationData
): SearchQueries {
  return {
    pubmed: generatePubMedQuery(data),
    cochrane: generateCochraneQuery(data),
    googleScholar: generateGoogleScholarQuery(data),
  };
}

/**
 * Generate PubMed search query with MeSH terms
 */
function generatePubMedQuery(data: ClinicalEvaluationData): string {
  const deviceTerms = data.Device.map((term) => `"${term}"`).join(" OR ");
  const conditionTerms = data.Condition.map((term) => `"${term}"`).join(" OR ");
  const populationTerms = data.Population.map((term) => `"${term}"`).join(
    " OR "
  );
  const outcomeTerms = data.Outcomes.map((term) => `"${term}"`).join(" OR ");

  return `(${deviceTerms})
AND (${conditionTerms})
AND (${populationTerms})
AND (${outcomeTerms})`;
}

/**
 * Generate Cochrane Library search query
 */
function generateCochraneQuery(data: ClinicalEvaluationData): string {
  const deviceTerms = data.Device.map((term) => `"${term}"`).join(" OR ");
  const conditionTerms = data.Condition.map((term) => `"${term}"`).join(" OR ");
  const populationTerms = data.Population.map((term) => `"${term}"`).join(
    " OR "
  );
  const outcomeTerms = data.Outcomes.map((term) => `"${term}"`).join(" OR ");

  return `(${deviceTerms})
AND (${conditionTerms})
AND (${populationTerms})
AND (${outcomeTerms})`;
}

/**
 * Generate Google Scholar search query
 */
function generateGoogleScholarQuery(data: ClinicalEvaluationData): string {
  const deviceTerms = data.Device.map((term) => `"${term}"`).join(" OR ");
  const conditionTerms = data.Condition.map((term) => `"${term}"`).join(" OR ");
  const outcomeTerms = data.Outcomes.map((term) => `"${term}"`).join(" OR ");

  return `(${deviceTerms})
(${conditionTerms})
(${outcomeTerms})`;
}

/**
 * Main function to perform complete clinical evaluation (fallback method)
 * This is used when OpenAI API is not available
 */
export function performClinicalEvaluation(
  input: ClinicalEvaluationInput
): ClinicalEvaluationResult {
  const validation = validateIntendedUse(input.intendedUse);

  let extractedData: ClinicalEvaluationData;
  let searchQueries: SearchQueries;

  if (validation.isValid) {
    extractedData = extractClinicalData(input.productName, input.intendedUse);
    searchQueries = generateSearchQueries(extractedData);
  } else {
    // Return empty data if validation fails
    extractedData = {
      Device: [],
      Condition: [],
      Population: [],
      Outcomes: [],
    };
    searchQueries = {
      pubmed: "",
      cochrane: "",
      googleScholar: "",
    };
  }

  return {
    validation,
    extractedData,
    searchQueries,
  };
}
