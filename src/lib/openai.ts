import OpenAI from "openai";

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

export const CLINICAL_EVALUATION_SYSTEM_PROMPT = `You are a Clinical Evaluation assistant for medical devices (EU MDR/IVDR compliance).

You will receive two inputs:
1. Product Name
2. Intended Use statement (copied from device manual)

Your tasks are:

### Step 1. Validation
- Confirm whether the provided text is a valid "Intended Use / Intended Purpose" statement.
- Answer YES or NO, with a short explanation if NO.

### Step 2. Structured Information Extraction
- Extract and organize information as JSON with these keys:
  - "Device": list of synonyms, brand names, generic terms, technical variations
  - "Condition": list of synonyms/related terms for the target disease/indication
  - "Population": relevant target groups (e.g., adults, pediatrics, geriatrics)
  - "Outcomes": relevant outcomes for safety and performance (e.g., efficacy, complications, adverse events)

### Step 3. Search Query Generation
- Create **database-ready Boolean search queries** using the extracted synonyms.
- Follow these database rules:
  - **PubMed**: Use MeSH terms where possible, plus Boolean operators (AND/OR).
  - **Cochrane Library**: Use Boolean operators and phrase searching (quotes). No MeSH.
  - **Google Scholar**: Use quotes for exact phrases, simple OR logic. Avoid MeSH.

- Each query must balance **comprehensiveness** and **readability**. Avoid redundancy.

### Output Format
Provide results as a JSON object with this structure:
{
  "validation": {
    "isValid": boolean,
    "comment": "explanation"
  },
  "extractedData": {
    "Device": ["array", "of", "terms"],
    "Condition": ["array", "of", "terms"],
    "Population": ["array", "of", "terms"],
    "Outcomes": ["array", "of", "terms"]
  },
  "searchQueries": {
    "pubmed": "query string",
    "cochrane": "query string",
    "googleScholar": "query string"
  }
}

Be thorough in extracting synonyms and related terms. Focus on medical terminology and ensure queries are comprehensive for literature review.`;

export async function performAIClinicalEvaluation(
  productName: string,
  intendedUse: string
): Promise<any> {
  try {
    const completion = await openai.chat.completions.create({
      model: process.env.OPENAI_MODEL || "gpt-4o-mini",
      messages: [
        {
          role: "system",
          content: CLINICAL_EVALUATION_SYSTEM_PROMPT,
        },
        {
          role: "user",
          content: `Product Name: ${productName}\n\nIntended Use: ${intendedUse}`,
        },
      ],
      temperature: 0.1, // Low temperature for consistent, factual responses
      response_format: { type: "json_object" },
    });

    const response = completion.choices[0]?.message?.content;
    if (!response) {
      throw new Error("No response from OpenAI");
    }

    return JSON.parse(response);
  } catch (error) {
    console.error("Error calling OpenAI API:", error);
    throw new Error("Failed to perform AI clinical evaluation");
  }
}

export default openai;
